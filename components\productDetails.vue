<template>
  <view>
    <up-popup :show="productDetailsShow" @close="closePopup" mode="bottom" round="10">
      <view class="product-details">
        <!-- 头部 -->
        <view class="header">
          <text class="cancel" @click="closePopup">取消</text>
          <text class="confirm" @click="handleConfirm">确定</text>
        </view>

        <!-- 内容区域 -->
        <scroll-view scroll-y class="content">
          <!-- 基本信息 -->
          <view class="section">
            <view class="info_title">
              <text class="info-title-text">基本信息</text>
            </view>
            <view class="info-content">
              <view class="info-row" v-for="(row, idx) in productFields" :key="idx">
                <view class="info-item" v-for="field in row" :key="field.label">
                  <text class="label info-item-label">{{ field.label }}</text>
                  <text class="value">{{ field.value }}</text>
                </view>
              </view>
              <view class="info-row">
                <view class="info-item" v-if="pricingInformation.total_stock">
                  <text class="label">库存</text>
                  <view class="value stock-display" v-if="type === 6">
                    <text class="original-stock">{{
                      pricingInformation.total_stock
                    }}</text>
                    <text class="used-stock" v-if="pricingInformation.remaining_stock > 0">-{{
                      pricingInformation.remaining_stock
                    }}</text>
                  </view>
                  <text class="value" v-else>{{ pricingInformation.total_stock }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 定价信息 -->
          <view class="section">
            <view class="info_title">
              <text class="info-title-text">定价信息</text>
            </view>
            <view class="form-item" v-if="type == 6 || type == 1 || type == 2 || type == 3">
              <text class="label form-item-label">单位</text>
              <view class="required">*</view>
              <view>
                <u--input v-model="pricingInformation.unit_name" placeholder="请选择单位" disabled disabledColor="#fff"
                  border="none" inputAlign="right" @tap="openUnitPickerShow"></u--input>
              </view>
            </view>
            <!-- 原数量 -->
            <view class="form-item" v-if="type == 0 || type == 3 || type == 4 || type == 5 || type == 7 || type == 8">
              <text class="label">原数量</text>
              <u--input v-model="pricingInformation.originalQuantity" border="none" placeholder="0" inputAlign="right"
                disabled disabledColor="color: #fff"></u--input>
            </view>
            <!-- 退货零售单 -->
            <view class="form-item" v-if="type == 7 || type == 8">
              <text class="label">已退货</text>
              <u--input v-model="pricingInformation.originalQuantity" border="none" placeholder="0" inputAlign="right"
                disabled disabledColor="color: #fff"></u--input>
            </view>
            <view class="form-item" v-if="pricingInformation.purchase_order_item && type !== 4 && type !== 5">
              <text class="label">已入库</text>
              <view class="required" style="left: 95rpx">*</view>
              <u--input v-model="pricingInformation.delivered_quantity" border="none" placeholder="请输入入库量" type="digit"
                inputAlign="right" disabled disabledColor="color: #fff"></u--input>
            </view>
            <!-- 退货单库存余量数量 -->
            <view class="form-item" v-if="type == 4 || type == 5">
              <text class="label">库存余量</text>
              <!-- <view class="required" style="left:95rpx">*</view> -->
              <u--input v-model="pricingInformation.remaining_quantity" border="none" placeholder="0" type="digit"
                inputAlign="right" disabled disabledColor="#fff"></u--input>
            </view>
            <view class="form-item vertical">
              <view class="inputContent">
                <text class="label">数量</text>
                <view class="required">*</view>
                <u--input v-model="pricingInformation.quantity" border="none" placeholder="请输入数量" type="number"
                  inputAlign="right" @change="handleQuantityChange"></u--input>
              </view>
              <text class="excessPrompt" v-if="
                (type == 4 || type == 5) &&
                pricingInformation.quantity >
                pricingInformation.remaining_quantity
              "><i-attention theme="outline" size="14" fill="#ff0000" />可退件数不足</text>
            </view>
            <view class="form-item">
              <text class="label">单价</text>
              <view class="required">*</view>
              <u--input v-model="pricingInformation.purchase_price" border="none" placeholder="请输入单价" type="digit"
                inputAlign="right"></u--input>
            </view>
            <view class="form-item">
              <text class="label">金额</text>
              <view class="required">*</view>
              <u--input v-model="pricingInformation.amount" border="none" placeholder="请输入金额" type="digit"
                inputAlign="right" disabled disabledColor="#fff"></u--input>
            </view>
            <view class="form-item">
              <text class="label">备注</text>
              <u--input v-model="pricingInformation.remark" border="none" placeholder="请输入备注"
                inputAlign="right"></u--input>
            </view>
          </view>
        </scroll-view>
        <view class="operation">
          <u-button type="error" text="删除" v-if="isShowDelBtn" @click="handleDelBtnClick"></u-button>
        </view>
      </view>
    </up-popup>
    <up-overlay :show="unitPickerShow" @click="unitPickerShow = false"></up-overlay>
    <up-picker :show="unitPickerShow" :columns="[unitPickerList]" keyName="label" @confirm="onUnitConfirm"
      @cancel="unitPickerShow = false"></up-picker>
  </view>
</template>


<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { formatNumber } from "@/utils/digitalConversion";
import eventBus from "@/utils/eventBus";

/**
 * 商品项接口定义
 */
interface MerchItem {
  id?: string | number; // 添加id属性
  item?: string;
  unit?: string;
  image?: string;
  item_name: string;
  name?: string; // 添加name字段
  code?: string;
  barcode?: string;
  originalQuantity?: number;
  delivered_quantity?: number;
  total_stock?: number;
  remaining_stock?: number;
  remaining_quantity?: number; // 添加remaining_quantity字段
  stock_quantity?: number;
  stock_remaining?: number;
  return_quantity?: number;
  purchase_price?: number;
  price?: number;
  unit_name?: string;
  quantity: number;
  conversion_rate?: number;
  purchase_order_item?: boolean;
  warehouse?: string;
  warehouse_name?: string;
  total?: number; // 添加total字段
  amount?: number; // 添加amount字段
  remark?: string; // 添加remark字段
  return_price?: number; // 添加return_price字段
  stock?: string; // 添加stock字段
  selected?: boolean; // 添加selected字段
  units?: any[]; // 添加units字段
  brand?: string; // 添加brand字段
  specification?: string; // 添加specification字段
  model?: string; // 添加model字段
  color?: string; // 添加color字段
}

/**
 * 定义组件属性
 */
const props = defineProps({
  selectGoodsList: {
    //被选中的商品列表
    type: Array as () => MerchItem[],
    default: () => [],
  },
  productDetailsShow: {
    //商品详情弹出层
    type: Boolean,
    default: false,
  },
  productData: {
    //商品详情数据
    type: Object as () => MerchItem,
    default: () => ({}),
  },
  isShowDelBtn: {
    //是否显示删除按钮
    type: Boolean,
    default: false,
  },
  isShowUnit: {
    //是否显示单位选择器
    type: Boolean,
    default: true,
  },
  isOpenFromOrder: {
    //是否从订单进入
    type: Boolean,
    default: false,
  },
  type: {
    /*
      0：进货关联订单
      1: 进货任意商品
      2: 采购订单
      4：退货单:关联订单进入
      5：退货单:任意商品进入
      6：零售订单
      7：零售退货订单:零售关联订单进入
      8: 零售退货订单:任意商品进入
        9：销售退货订单:销售关联订单进入
        10: 销售退货订单:任意商品进入
    */
    type: Number,
    default: 0,
  },
});

/**
 * 定义组件事件
 */
const emit = defineEmits(['close', 'confirm', 'delBtnPricing']);

// 响应式数据
const pricingInformation = reactive<MerchItem>({
  item_name: "",
  quantity: 0,
  price: 0,
  total: 0,
  // 初始化其他可能用到的字段
  stock: "",
  originalQuantity: 0,
  purchase_price: 0,
  return_price: 0,
  amount: 0,
  remark: "",
  unit_name: "",
  unit: "",
  item: "",
  conversion_rate: 1,
});
const unitPickerShow = ref(false);
const unitPickerList = ref<any[]>([]); // [{value: unit, label: unit_name}]
const selectedUnit = ref<any>(null); // 选中的unit对象
const returnWarehouse = reactive({
  warehouse: "",
  warehouse_name: "",
}); //退货时选择的仓库
// 用于记录已选商品的数量统计
const lastInputQuantity = ref(0); // 记录上一次输入的数量
const initialRemainingStock = ref(0); // 进入商品详细层时候需要的数量

// 计算属性：基本信息字段
const productFields = computed(() => {
  const fields = [];
  if (props.productData.code)
    fields.push({ label: "批次号", value: props.productData.code });
  if (props.productData.name || props.productData.item_name)
    fields.push({
      label: "名称",
      value: props.productData.name || props.productData.item_name,
    });
  if (
    props.productData.units &&
    props.productData.units.length &&
    props.type !== 4 &&
    props.type !== 5 &&
    props.type !== 7 &&
    props.type !== 8
  ) {
    fields.push({
      label: "单位",
      value: props.productData.units.map((i: any) => i.unit_type_name).join("，"),
    });
  } else if (props.productData.unit_name) {
    fields.push({
      label: "单位",
      value: props.productData.unit_name,
    });
  }

  if (props.productData.brand)
    fields.push({ label: "品牌", value: props.productData.brand });
  if (props.productData.specification)
    fields.push({ label: "规格", value: props.productData.specification });
  if (props.productData.model)
    fields.push({ label: "型号", value: props.productData.model });
  if (props.productData.color)
    fields.push({ label: "颜色", value: props.productData.color });

  // 每两个一组
  const rows = [];
  for (let i = 0; i < fields.length; i += 2) {
    rows.push(fields.slice(i, i + 2));
  }
  return rows;
});

// 监听 productDetailsShow 属性变化
watch(() => props.productDetailsShow, (val) => {
  if (val) {
    console.log(props.productData);

    // 使用 Object.assign 确保响应式更新
    Object.assign(pricingInformation, {
      ...props.productData,
      // 确保数值类型正确
      quantity: Number(props.productData.quantity) || 0,
      purchase_price: Number(props.productData.purchase_price || props.productData.price) || 0,
      amount: Number(props.productData.total) || 0,
    });

    // 特殊处理 unit_name 和 unit
    pricingInformation.unit_name = props.productData.unit_name;
    pricingInformation.unit = props.productData.unit;

    console.log(pricingInformation.unit_name);
    console.log(pricingInformation.unit);
    console.log(pricingInformation);

    if (props.productData.name) {
      pricingInformation.item_name = props.productData.name;
    }

    if (props.type === 0 || props.type === 3 || props.type === 4 || props.type === 7 || props.type === 8) {
      pricingInformation.originalQuantity = Number(props.productData.quantity);
      pricingInformation.quantity = 0; // 初始数量设为0，等待用户输入
    }

    if (props.type === 4 || props.type === 5) {
      pricingInformation.stock = String(props.productData.id || '');
      returnWarehouse.warehouse = props.productData.warehouse || '';
      returnWarehouse.warehouse_name = props.productData.warehouse_name || '';
    }
    console.log(pricingInformation);

    console.log(props.isShowUnit);

    if (props.productData.units && props.isShowUnit) {
      unitPickerList.value = props.productData.units.map((u: any, index: number) => ({
        value: u.unit_type_id, // 使用 unit_type_id 作为 value
        name: u.unit_type_name,
        conversion_rate: u.conversion_rate,
        label:
          index === 0
            ? u.unit_type_name
            : u.unit_type_name +
            "(" +
            String(u.conversion_rate).split(".")[0] +
            (props.productData.units?.[0]?.unit_type_name || '') +
            ")",
      }));
      // 默认选中第一个单位
      if (unitPickerList.value.length > 0) {
        pricingInformation.unit_name = unitPickerList.value[0].name;
        pricingInformation.unit = unitPickerList.value[0].value;
        pricingInformation.conversion_rate = unitPickerList.value[0].conversion_rate;
      }
    }

    //当零售退货单时，需要将原本单价赋值
    if (props.type === 7 || props.type === 8) {
      pricingInformation.purchase_price = Number(formatNumber(Number(props.productData.price)));
    }
  }
}, { deep: true, immediate: true });

// 监听单价变化，更新金额
watch(() => pricingInformation.purchase_price, (val) => {
  if (props.type === 4 || props.type === 5) {
    pricingInformation.return_price = val;
  }
  pricingInformation.amount = Number(pricingInformation.quantity) * Number(val);
});

// 监听数量变化，处理各种逻辑
watch(() => pricingInformation.quantity, (val) => {
  console.log('输入的val:', val, typeof val);
  console.log('remaining_quantity:', pricingInformation.remaining_quantity, typeof pricingInformation.remaining_quantity);
  console.log('type:', props.type);

  const numVal = Number(val);
  const numRemaining = Number(pricingInformation.remaining_quantity);

  console.log('转换后比较:', numVal, '>', numRemaining, '=', numVal > numRemaining);

  if (
    (props.type === 4 || props.type === 5) && numVal > numRemaining
  ) {
    console.log('进入限制逻辑，因为', numVal, '>', numRemaining);
    uni.showToast({
      title: "数量不能大于原数量",
      icon: "none",
      mask: true,
    });
    pricingInformation.quantity = numRemaining;
    pricingInformation.amount =
      Number(pricingInformation.purchase_price) * numRemaining;
    return;
  } else {
    pricingInformation.amount =
      Number(pricingInformation.purchase_price) * numVal;
  }
});

// 删除按钮点击
const handleDelBtnClick = () => {
  emit("delBtnPricing", pricingInformation);
};

// 打开单位选择器
const openUnitPickerShow = () => {
  if (!props.isShowUnit) {
    return;
  }
  if (props.isShowDelBtn) {
    return;
  }
  if (props.type === 0) {
    return;
  }
  if (!pricingInformation.purchase_order_item) {
    unitPickerShow.value = true;
    return;
  }

  pricingInformation.unit_name = unitPickerList.value[0].name;
  pricingInformation.unit = unitPickerList.value[0].value;
  pricingInformation.conversion_rate =
    unitPickerList.value[0].conversion_rate;
  unitPickerShow.value = true;
};

// 关闭弹出层
const closePopup = () => {
  unitPickerShow.value = false;
  emit("close", pricingInformation);
  resetPricingInformation();
};

// 提交
const handleConfirm = () => {
  if (pricingInformation.unit_name === "") {
    uni.showToast({
      title: "请选择单位",
      icon: "none",
      mask: true,
    });
    return;
  }
  if (pricingInformation.quantity === 0) {
    uni.showToast({
      title: "请输入数量",
      icon: "none",
      mask: true,
    });
    return;
  }
  if (Number(pricingInformation.quantity) === 0) {
    uni.showToast({
      title: "数量不能等于0",
      icon: "none",
      mask: true,
    });
    return;
  }
  if (pricingInformation.purchase_price === 0) {
    uni.showToast({
      title: "请输入单价",
      icon: "none",
      mask: true,
    });
    return;
  }
  if (Number(pricingInformation.purchase_price) === 0) {
    uni.showToast({
      title: "单价不能等于0",
      icon: "none",
      mask: true,
    });
    return;
  }
  if (props.type !== 6) {
    uni.setStorageSync("returnWarehouse", returnWarehouse);
    eventBus.$emit("returnWarehouse", returnWarehouse);
  }
  pricingInformation.selected = true;
  unitPickerShow.value = false;

  emit("close", pricingInformation);
  emit("confirm", pricingInformation);
  resetPricingInformation();
};

const handleCancel = () => {
  resetPricingInformation();
};

// 重置定价信息
const resetPricingInformation = () => {
  Object.assign(pricingInformation, {
    stock: "", //库存追踪号
    originalQuantity: 0, //原数量
    quantity: 0, //数量
    purchase_price: 0, //单价
    return_price: 0, //退货单价
    amount: 0, //金额
    remark: "", //备注
    unit_name: "", //单位
    unit: "", //单位id
    item_name: "", //商品名称
    item: "", //商品id
  });
  lastInputQuantity.value = 0; // 重置 lastInputQuantity
};

// 选择单位
const onUnitConfirm = (e: any) => {
  selectedUnit.value = e.value[0];
  unitPickerShow.value = false;
  pricingInformation.unit_name = selectedUnit.value.name;
  pricingInformation.unit = selectedUnit.value.value;
  pricingInformation.conversion_rate = selectedUnit.value.conversion_rate;
  // 关键：用新 conversion_rate 重新计算
  handleQuantityChange(
    pricingInformation.quantity,
    selectedUnit.value.conversion_rate
  );
};

// 处理数量变化的方法
const handleQuantityChange = (value: any, conversion_rate: any) => {
  const quantity = Number(value) || 0;
  const conversionRate = conversion_rate || Number(pricingInformation.conversion_rate) || 1;
  const actualQuantity = quantity * conversionRate;

  // 计算并更新金额
  calculateAndUpdateAmount(quantity);

  // 处理零售订单库存逻辑
  if (props.type === 6) {
    handleRetailOrderStock(quantity, conversionRate, actualQuantity);
    return;
  }

  // 检查退货单数量限制
  if ((props.type === 4 || props.type === 5)) {
    checkReturnOrderQuantityLimit(quantity);
    return;
  }

  // 检查零售退货单数量限制
  if ((props.type === 7 || props.type === 8)) {
    checkRetailReturnOrderQuantityLimit(quantity);
    return;
  }

  // 更新已处理的数量（如果需要的话可以在这里添加相关逻辑）
};

// 计算并更新金额
const calculateAndUpdateAmount = (quantity: number) => {
  pricingInformation.amount = Number(pricingInformation.purchase_price) * quantity;
};

// 处理零售订单库存逻辑
const handleRetailOrderStock = (quantity: number, conversionRate: number, actualQuantity: number) => {
  // 确保remaining_stock有初始值，如果没有值或为0，设为0
  if (!pricingInformation.remaining_stock && pricingInformation.remaining_stock !== 0) {
    pricingInformation.remaining_stock = 0;
  }

  // 获取最开始的remaining_stock值
  initialRemainingStock.value = Number(props.productData.remaining_stock) || 0;

  // 计算新的库存减少量：最开始的remaining_stock + 当前数量乘以转换率
  const newTotalReduction = initialRemainingStock.value + actualQuantity;
  console.log(newTotalReduction);

  // 判断是否超出现有库存
  if (newTotalReduction > Number(pricingInformation.total_stock)) {
    uni.showToast({
      title: "当前库存不足,已帮您自动调整为最大数量",
      icon: "none",
      mask: true,
    });

    // 计算最大可用数量：(总库存 - 最开始的remaining_stock) / 转换率，向下取整
    const maxQuantity = Math.floor(
      (Number(pricingInformation.total_stock) - initialRemainingStock.value) / conversionRate
    );

    pricingInformation.quantity = maxQuantity;
    calculateAndUpdateAmount(maxQuantity);

    // 更新库存减少量
    pricingInformation.remaining_stock = initialRemainingStock.value + (maxQuantity * conversionRate);
    return;
  }

  // 实时更新库存减少量
  pricingInformation.remaining_stock = newTotalReduction;
  console.log(pricingInformation);
  // 强制更新对象触发视图更新
  // this.$set(pricingInformation, 'remaining_stock', newTotalReduction); // $set is not needed in Vue 3
  // this.$forceUpdate(); // $forceUpdate is not needed in Vue 3
};

// 检查退货单数量限制
const checkReturnOrderQuantityLimit = (quantity: number) => {
  const remainingQuantity = pricingInformation.remaining_quantity || 0;
  if (quantity > remainingQuantity) {
    uni.showToast({
      title: "数量不能大于原数量",
      icon: "none",
      mask: true,
    });
    pricingInformation.quantity = remainingQuantity;
    calculateAndUpdateAmount(remainingQuantity);
    lastInputQuantity.value = remainingQuantity;
  }
};

// 检查零售退货单数量限制
const checkRetailReturnOrderQuantityLimit = (quantity: number) => {
  const originalQuantity = pricingInformation.originalQuantity || 0;
  if (quantity > originalQuantity) {
    uni.showToast({
      title: "数量不能大于原数量",
      icon: "none",
      mask: true,
    });

    // 方法1：使用 $nextTick 确保 DOM 更新
    nextTick(() => {
      pricingInformation.quantity = originalQuantity;
      calculateAndUpdateAmount(originalQuantity);

      // 强制更新输入框的值
      nextTick(() => {
        // $forceUpdate is not needed in Vue 3
      });
    });

    console.log('pricingInformation.quantity: ' + pricingInformation.quantity);
  }
};


</script>

<style lang="scss" scoped>
.product-details {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  min-height: 60vh;
  max-height: 90vh;
  display: flex;
  flex-direction: column;

  .header {
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eee;

    .cancel,
    .confirm {
      font-size: 28rpx;
    }

    .cancel {
      color: #333;
    }

    .confirm {
      color: #5a93e5;
    }
  }

  .content {
    flex: 1;
    padding: 0 30rpx;
    width: 93%;

    .section {
      margin-bottom: 30rpx;

      .info-content {
        padding: 20rpx 0;

        .info-row {
          display: flex;
          padding: 20rpx 0;
          border-bottom: 1px solid #eee;

          .info-item {
            display: flex;
            align-items: center;
            width: 50%;

            .info-item-label {
              width: 90rpx;
              display: inline-block;
              text-align: justify;
              text-align-last: justify;
              color: #333;
              font-size: 28rpx;
              margin-right: 20rpx;
              min-width: 80rpx;
            }

            .value {
              color: #666;
              font-size: 28rpx;
              flex: 1;
              text-align: center;
            }
          }
        }
      }

      .form-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1px solid #eee;
        position: relative;

        .form-item-label {
          font-size: 28rpx;
          color: #333;
        }

        .required {
          position: absolute;
          color: red;

          left: 65rpx;
        }

        ::v-deep .u-input {
          flex: 1;
          margin-left: 20rpx;

          .u-input__input {
            text-align: right;
            color: #666;
          }
        }
      }
    }
  }
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unitArea {
  max-height: 300rpx;
  width: 300rpx;
  background-color: #fff;
  border: 1px solid #cacaca;
  border-radius: 20rpx;
  overflow: auto;
  position: fixed;
  z-index: 999;
  right: 20rpx;
  margin-top: 390rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

  .unitArea_search {
    position: sticky;
    top: 0;
    width: 90%;
    border-bottom: 1px solid #cacaca;
    padding: 10rpx;
    background-color: #fff;
    z-index: 999;
  }

  .unitArea_item {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;
    border-bottom: 1px solid #cacaca;
    font-size: 25rpx;
    font-weight: 700;
  }

  .noResults {
    width: 100%;
    height: 50rpx;
    line-height: 50rpx;
    display: flex;
    justify-content: center;
  }

  .unitArea_content {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }

  .unitArea_item {
    background-color: #fff;
    border-radius: 4rpx;
    font-size: 28rpx;
    color: #333;

    &:active {
      background-color: #e0e0e0;
    }
  }
}

.vertical {
  display: flex;
  flex-direction: column;

  .inputContent {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
}

.excessPrompt {
  color: red;
  font-size: 24rpx;
  width: 100%;
  display: flex;
  flex-direction: row-reverse;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 125rpx;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

.stock-display {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8rpx;

  .original-stock {
    color: #333;
    font-size: 28rpx;
  }

  .used-stock {
    color: #ff4444;
    font-size: 24rpx;
  }
}
</style>